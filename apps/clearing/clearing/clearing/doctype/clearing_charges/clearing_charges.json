{"actions": [], "allow_rename": 1, "autoname": "format:CC-{clearing_file}-{#####}", "creation": "2024-07-15 23:20:46.142648", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["consigee_details_section", "clearing_file", "column_break_ahtow", "status", "consigee", "section_break_ccym", "disbursement", "service_charge_details_section", "charges", "section_break_0qrth", "column_break_wv5mn", "transport_total", "agency_fee", "column_break_fp5jw", "tra_clearance_total", "port_clearance_total", "shipment_clearance_total", "physical_clearance_total", "total_debit", "column_break_1", "reference_number", "reference_date", "column_break_hqjie", "generate_invoice"], "fields": [{"fieldname": "service_charge_details_section", "fieldtype": "Section Break", "label": "Charge Details"}, {"fieldname": "column_break_1", "fieldtype": "Section Break", "label": "Invoice and Debit Details"}, {"fieldname": "consigee_details_section", "fieldtype": "Section Break", "label": "Consigee Details"}, {"fieldname": "clearing_file", "fieldtype": "Link", "in_list_view": 1, "label": "Clearing File", "options": "Clearing File", "reqd": 1}, {"fieldname": "column_break_ahtow", "fieldtype": "Column Break"}, {"fetch_from": "clearing_file.customer", "fieldname": "consigee", "fieldtype": "Link", "in_list_view": 1, "label": "Consigee", "options": "Customer", "read_only": 1}, {"fieldname": "charges", "fieldtype": "Table", "label": "Charges", "options": "Clearing Charge Detail", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Payment Status", "options": "Draft\nReturn\nCredit Note Issued\nSubmitted\nPaid\nPartly Paid\nUnpaid\nUnpaid and Discounted\nPartly Paid and Discounted\nOverdue and Discounted\nOverdue\nCancelled\nInternal Transfer", "read_only": 1}, {"fieldname": "generate_invoice", "fieldtype": "<PERSON><PERSON>", "label": "Generate Invoice"}, {"fieldname": "column_break_hqjie", "fieldtype": "Column Break"}, {"fieldname": "tra_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "TRA Clearance Total"}, {"fieldname": "port_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Port Clearance Total"}, {"fieldname": "shipment_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipment Clearance Total"}, {"fieldname": "physical_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Physical Verification Total"}, {"fieldname": "section_break_0qrth", "fieldtype": "Section Break"}, {"fieldname": "column_break_wv5mn", "fieldtype": "Column Break"}, {"fieldname": "column_break_fp5jw", "fieldtype": "Column Break"}, {"fieldname": "section_break_ccym", "fieldtype": "Section Break"}, {"fieldname": "disbursement", "fieldtype": "Table", "label": "Disbursement", "options": "Consignee Disbursement"}, {"fieldname": "transport_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Transport Total"}, {"fieldname": "agency_fee", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Clearing Agency Fee Total"}, {"fieldname": "total_debit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "in_standard_filter": 1, "label": "Total Debit Charges"}, {"fieldname": "reference_number", "fieldtype": "Link", "in_list_view": 1, "label": "Reference number", "options": "Sales Invoice", "read_only": 1}, {"fetch_from": "reference_number.posting_date", "fieldname": "reference_date", "fieldtype": "Date", "label": "Reference Date", "read_only": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-18 12:27:37.005417", "modified_by": "Administrator", "module": "Clearing", "name": "Clearing Charges", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Clearing Agent", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "TRA Clearing Agent", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}