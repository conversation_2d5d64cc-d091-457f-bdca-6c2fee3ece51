2025-02-26 12:04:42,132 INFO ipython === bench console session ===
2025-02-26 12:04:42,133 INFO ipython values = frappe.db.get_values("Property Setter")
print(values)
2025-02-26 12:04:42,134 INFO ipython === session end ===
2025-04-18 15:02:33,243 INFO ipython === bench console session ===
2025-04-18 15:02:33,244 INFO ipython # List all Custom DocPerm entries for the role
frappe.get_all("Custom DocPerm", filters={"role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,244 INFO ipython # In Frappe Bench Console
frappe.get_doc("Custom DocPerm", {"parent": "Customer", "role": "Test Custom Role"})
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Customer", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,245 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoices", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Sales Invoice", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,246 INFO ipython # In Frappe Bench Console
custom_docperm = frappe.get_all(
    "Custom DocPerm",
    filters={"parent": "Asset", "role": "Test Custom Role"},
    fields=["*"]
)
print(custom_docperm)  # Should return entries if auto-permission worked
2025-04-18 15:02:33,247 INFO ipython === session end ===
2025-08-10 12:46:27,873 INFO ipython === bench console session ===
2025-08-10 12:46:27,874 INFO ipython frappe.get_all("Clearing File", limit=5)
2025-08-10 12:46:27,875 INFO ipython frappe.get_doc("DocType", "Clearing File").permissions
2025-08-10 12:46:27,875 INFO ipython for perm in frappe.get_doc("DocType", "Clearing File").permissions: print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}")
2025-08-10 12:46:27,875 INFO ipython frappe.session.user
2025-08-10 12:46:27,875 INFO ipython === session end ===
2025-08-10 12:53:24,425 INFO ipython === bench console session ===
2025-08-10 12:53:24,425 INFO ipython frappe.get_doc("User", "<EMAIL>").roles
2025-08-10 12:53:24,426 INFO ipython user_roles = [role.role for role in frappe.get_doc("User", "<EMAIL>").roles]
2025-08-10 12:53:24,426 INFO ipython print("User roles:", user_roles)
2025-08-10 12:53:24,426 INFO ipython print("Has System Manager:", "System Manager" in user_roles)
2025-08-10 12:53:24,426 INFO ipython # Check user permissions for Clearing File
2025-08-10 12:53:24,426 INFO ipython user_perms = frappe.get_all("User Permission", filters={"user": "<EMAIL>", "allow": "Clearing File"})
2025-08-10 12:53:24,426 INFO ipython print("User Permissions for Clearing File:", user_perms)
2025-08-10 12:53:24,426 INFO ipython # Check if there are any company restrictions
2025-08-10 12:53:24,426 INFO ipython company_perms = frappe.get_all("User Permission", filters={"user": "<EMAIL>", "allow": "Company"})
2025-08-10 12:53:24,426 INFO ipython print("Company Permissions:", company_perms)
2025-08-10 12:53:24,427 INFO ipython # Set user context to simulate login
2025-08-10 12:53:24,427 INFO ipython frappe.set_user("<EMAIL>")
2025-08-10 12:53:24,427 INFO ipython print("Current user:", frappe.session.user)
2025-08-10 12:53:24,427 INFO ipython # Try to get clearing files as this user
2025-08-10 12:53:24,427 INFO ipython try:
        clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
            print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,427 INFO ipython     for cf in clearing_files:
            print(f"  - {cf.name}: {cf.customer} ({cf.status})")
        except Exception as e:
2025-08-10 12:53:24,427 INFO ipython     print("Error accessing clearing files:", str(e))
2025-08-10 12:53:24,427 INFO ipython try:
        clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
            print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,427 INFO ipython     for cf in clearing_files:
            print(f"  - {cf.name}: {cf.customer} ({cf.status})")
        except Exception as e:
2025-08-10 12:53:24,427 INFO ipython     print("Error accessing clearing files:", str(e))
2025-08-10 12:53:24,428 INFO ipython clearing_files = frappe.get_all("Clearing File", limit=5, fields=["name", "customer", "status"])
2025-08-10 12:53:24,428 INFO ipython print("Clearing files accessible:", len(clearing_files))
2025-08-10 12:53:24,428 INFO ipython # Check if the clearing module is visible to the user
2025-08-10 12:53:24,428 INFO ipython modules = frappe.get_all("Module Def", fields=["name", "app_name"])
2025-08-10 12:53:24,428 INFO ipython clearing_module = [m for m in modules if m.app_name == "clearing"]
2025-08-10 12:53:24,428 INFO ipython print("Clearing modules:", clearing_module)
2025-08-10 12:53:24,428 INFO ipython # Check workspace permissions
2025-08-10 12:53:24,428 INFO ipython workspace = frappe.get_doc("Workspace", "Clearing & Forwarding")
2025-08-10 12:53:24,428 INFO ipython print("Workspace public:", workspace.public)
2025-08-10 12:53:24,428 INFO ipython print("Workspace roles:", workspace.roles)
2025-08-10 12:53:24,429 INFO ipython # Check if there are any default filters or restrictions
2025-08-10 12:53:24,429 INFO ipython doctype_meta = frappe.get_meta("Clearing File")
2025-08-10 12:53:24,429 INFO ipython print("Clearing File has_web_view:", doctype_meta.has_web_view)
2025-08-10 12:53:24,429 INFO ipython print("Clearing File is_submittable:", doctype_meta.is_submittable)
2025-08-10 12:53:24,429 INFO ipython print("Clearing File track_changes:", doctype_meta.track_changes)
2025-08-10 12:53:24,429 INFO ipython # Check if user has any saved filters that might be causing issues
2025-08-10 12:53:24,429 INFO ipython saved_filters = frappe.get_all("List Filter", filters={"for_user": "<EMAIL>", "reference_doctype": "Clearing File"})
2025-08-10 12:53:24,429 INFO ipython print("Saved filters for Clearing File:", saved_filters)
2025-08-10 12:53:24,429 INFO ipython # Check if there are any custom list view settings
2025-08-10 12:53:24,429 INFO ipython list_settings = frappe.get_all("List View Settings", filters={"name": "Clearing File"})
2025-08-10 12:53:24,429 INFO ipython print("List View Settings:", list_settings)
2025-08-10 12:53:24,430 INFO ipython # Check if there are any custom permissions or restrictions in the clearing file python file
2025-08-10 12:53:24,430 INFO ipython === session end ===
2025-09-19 14:55:28,362 INFO ipython === bench console session ===
2025-09-19 14:55:28,363 INFO ipython import frappe
2025-09-19 14:55:28,363 INFO ipython # Check if clearing charges exist
2025-09-19 14:55:28,363 INFO ipython clearing_charges = frappe.get_all("Clearing Charges", fields=["name", "status", "reference_number"], limit=5)
2025-09-19 14:55:28,363 INFO ipython print("Clearing Charges found:", len(clearing_charges))
2025-09-19 14:55:28,363 INFO ipython for cc in clearing_charges:
        print(f"  {cc.name}: Status={cc.status}, Invoice={cc.reference_number}")
        for cc in clearing_charges:
                print(f"  {cc.name}: Status={cc.status}, Invoice={cc.reference_number}")
                
2025-09-19 14:55:28,363 INFO ipython # Test the hook function
2025-09-19 14:55:28,364 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import handle_invoice_status_change, update_clearing_charges_from_invoice_change
2025-09-19 14:55:28,364 INFO ipython # Test with a real invoice
2025-09-19 14:55:28,364 INFO ipython invoice_name = "ACC-SINV-2025-00051"
2025-09-19 14:55:28,364 INFO ipython invoice = frappe.get_doc("Sales Invoice", invoice_name)
2025-09-19 14:55:28,364 INFO ipython print(f"Invoice {invoice_name} current status: {invoice.status}")
2025-09-19 14:55:28,364 INFO ipython # Test the update function directly
2025-09-19 14:55:28,364 INFO ipython result = update_clearing_charges_from_invoice_change(invoice_name, invoice.status)
2025-09-19 14:55:28,364 INFO ipython print(f"Update result: {result}")
2025-09-19 14:55:28,364 INFO ipython # Check if the status was updated
2025-09-19 14:55:28,365 INFO ipython cc_after = frappe.get_all("Clearing Charges", filters={"reference_number": invoice_name}, fields=["name", "status"])
2025-09-19 14:55:28,365 INFO ipython print("Clearing charges after update:")
2025-09-19 14:55:28,365 INFO ipython for cc in cc_after:
        print(f"  {cc.name}: Status={cc.status}")
        
2025-09-19 14:55:28,365 INFO ipython # Now test the hook function
2025-09-19 14:55:28,365 INFO ipython print("\nTesting hook function...")
2025-09-19 14:55:28,365 INFO ipython handle_invoice_status_change(invoice, "on_update_after_submit")
2025-09-19 14:55:28,365 INFO ipython print("Hook function executed")
2025-09-19 14:55:28,365 INFO ipython # Let's test if the hooks are properly registered
2025-09-19 14:55:28,365 INFO ipython import frappe.hooks
2025-09-19 14:55:28,366 INFO ipython # Check if our hooks are registered
2025-09-19 14:55:28,366 INFO ipython hooks = frappe.get_hooks()
2025-09-19 14:55:28,366 INFO ipython print("Sales Invoice hooks:")
2025-09-19 14:55:28,366 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                    print(f"  {event}: {methods}")
                    else:
                            print("  No Sales Invoice hooks found!")
2025-09-19 14:55:28,366 INFO ipython # Let's also check the clearing app hooks specifically
2025-09-19 14:55:28,366 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 14:55:28,366 INFO ipython print("\nClearing app doc_events:")
2025-09-19 14:55:28,366 INFO ipython if hasattr(clearing_hooks, 'doc_events'):
        print(clearing_hooks.doc_events.get("Sales Invoice", "Not found"))
        else:
                print("No doc_events found in clearing hooks")
2025-09-19 14:55:28,366 INFO ipython # Check hooks properly
2025-09-19 14:55:28,366 INFO ipython hooks = frappe.get_hooks()
2025-09-19 14:55:28,367 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                    print(f"  {event}: {methods}")
                    else:
                            print("  No Sales Invoice hooks found!")
2025-09-19 14:55:28,367 INFO ipython # Check clearing hooks
2025-09-19 14:55:28,367 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 14:55:28,367 INFO ipython if hasattr(clearing_hooks, 'doc_events'):
        si_hooks = clearing_hooks.doc_events.get("Sales Invoice", "Not found")
            print(f"\nClearing Sales Invoice hooks: {si_hooks}")
2025-09-19 14:55:28,367 INFO ipython else:
        print("No doc_events found in clearing hooks")
2025-09-19 14:55:28,367 INFO ipython === session end ===
2025-09-19 16:49:22,959 INFO ipython === bench console session ===
2025-09-19 16:49:22,960 INFO ipython import frappe
2025-09-19 16:49:22,960 INFO ipython # Check if hooks are registered
2025-09-19 16:49:22,961 INFO ipython hooks = frappe.get_hooks()
2025-09-19 16:49:22,961 INFO ipython print("Checking Sales Invoice hooks...")
2025-09-19 16:49:22,961 INFO ipython if "Sales Invoice" in hooks.get("doc_events", {}):
        print("Sales Invoice hooks found:")
            for event, methods in hooks["doc_events"]["Sales Invoice"].items():
                        print(f"  {event}: {methods}")
2025-09-19 16:49:22,961 INFO ipython else:
        print("No Sales Invoice hooks found in system!")
2025-09-19 16:49:22,962 INFO ipython # Check clearing app hooks
2025-09-19 16:49:22,962 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 16:49:22,962 INFO ipython print(f"\nClearing app Sales Invoice hooks:")
2025-09-19 16:49:22,962 INFO ipython if hasattr(clearing_hooks, 'doc_events') and "Sales Invoice" in clearing_hooks.doc_events:
        print(clearing_hooks.doc_events["Sales Invoice"])
        else:
                print("Not found in clearing hooks")
2025-09-19 16:49:22,963 INFO ipython # Simple check
2025-09-19 16:49:22,963 INFO ipython from clearing import hooks as clearing_hooks
2025-09-19 16:49:22,963 INFO ipython print("Clearing hooks doc_events:")
2025-09-19 16:49:22,963 INFO ipython print(clearing_hooks.doc_events)
2025-09-19 16:49:22,963 INFO ipython # Check if our function exists
2025-09-19 16:49:22,964 INFO ipython from clearing.clearing.doctype.clearing_charges.clearing_charges import handle_invoice_status_change
2025-09-19 16:49:22,964 INFO ipython print("\nFunction imported successfully")
2025-09-19 16:49:22,964 INFO ipython # Let's test if the hooks are working by simulating an invoice status change
2025-09-19 16:49:22,964 INFO ipython invoice_name = "ACC-SINV-2025-00051"
2025-09-19 16:49:22,964 INFO ipython invoice = frappe.get_doc("Sales Invoice", invoice_name)
2025-09-19 16:49:22,964 INFO ipython print(f"Current invoice status: {invoice.status}")
2025-09-19 16:49:22,964 INFO ipython # Let's manually change the status and see if it triggers
2025-09-19 16:49:22,965 INFO ipython old_status = invoice.status
2025-09-19 16:49:22,965 INFO ipython invoice.status = "Paid"
2025-09-19 16:49:22,965 INFO ipython print(f"Changed status to: {invoice.status}")
2025-09-19 16:49:22,965 INFO ipython # Test the hook function manually
2025-09-19 16:49:22,965 INFO ipython handle_invoice_status_change(invoice, "on_update_after_submit")
2025-09-19 16:49:22,965 INFO ipython # Check if clearing charges were updated
2025-09-19 16:49:22,966 INFO ipython cc_updated = frappe.get_all("Clearing Charges", filters={"reference_number": invoice_name}, fields=["name", "status"])
2025-09-19 16:49:22,966 INFO ipython print(f"Clearing charges status after manual hook call:")
2025-09-19 16:49:22,966 INFO ipython for cc in cc_updated:
        print(f"  {cc.name}: {cc.status}")
        
2025-09-19 16:49:22,966 INFO ipython # Restore original status
2025-09-19 16:49:22,966 INFO ipython invoice.status = old_status
2025-09-19 16:49:22,966 INFO ipython === session end ===
2025-09-19 17:14:23,436 INFO ipython === bench console session ===
2025-09-19 17:14:23,437 INFO ipython import frappe
2025-09-19 17:14:23,437 INFO ipython # Test the new transit document checking functionality
2025-09-19 17:14:23,437 INFO ipython clearing_files = frappe.get_all("Clearing File", fields=["name", "status"], limit=3)
2025-09-19 17:14:23,438 INFO ipython print("Available clearing files:")
2025-09-19 17:14:23,442 INFO ipython for cf in clearing_files:
        print(f"  {cf.name}: Status={cf.status}")
        
2025-09-19 17:14:23,442 INFO ipython # Test with the first one if available
2025-09-19 17:14:23,443 INFO ipython if clearing_files:
        test_cf_name = clearing_files[0].name
            test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 17:14:23,443 INFO ipython     print(f"\nTesting with: {test_cf_name}")
2025-09-19 17:14:23,443 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 17:14:23,443 INFO ipython     print(f"Attached documents:")
2025-09-19 17:14:23,443 INFO ipython     if test_cf.document:
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
                        else:
                                print("  No documents attached")
2025-09-19 17:14:23,444 INFO ipython # Test properly
2025-09-19 17:14:23,444 INFO ipython test_cf_name = clearing_files[0].name
2025-09-19 17:14:23,444 INFO ipython test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 17:14:23,444 INFO ipython print(f"Testing with: {test_cf_name}")
2025-09-19 17:14:23,444 INFO ipython print(f"Current status: {test_cf.status}")
2025-09-19 17:14:23,444 INFO ipython print("Attached documents:")
2025-09-19 17:14:23,445 INFO ipython if test_cf.document:
        for doc in test_cf.document:
                    print(f"  - {doc.document_name}")
                    else:
                            print("  No documents attached")
2025-09-19 17:14:23,445 INFO ipython # Test the new function
2025-09-19 17:14:23,445 INFO ipython print("\nTesting transit document check...")
2025-09-19 17:14:23,445 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:14:23,445 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:14:23,445 INFO ipython # Check documents properly
2025-09-19 17:14:23,446 INFO ipython if test_cf.document:
        print("Documents found:")
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
2025-09-19 17:14:23,446 INFO ipython else:
        print("No documents attached")
2025-09-19 17:14:23,446 INFO ipython # Let's manually add the required documents to test
2025-09-19 17:14:23,446 INFO ipython print("\nAdding test documents...")
2025-09-19 17:14:23,446 INFO ipython # Clear existing documents first
2025-09-19 17:14:23,446 INFO ipython test_cf.document = []
2025-09-19 17:14:23,447 INFO ipython # Add required transit documents
2025-09-19 17:14:23,447 INFO ipython required_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 17:14:23,447 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:14:23,447 INFO ipython print("Added documents:")
2025-09-19 17:14:23,447 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:14:23,447 INFO ipython # Test the function again
2025-09-19 17:14:23,448 INFO ipython print("\nTesting transit document check with all required docs...")
2025-09-19 17:14:23,448 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:14:23,448 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:14:23,448 INFO ipython === session end ===
2025-09-19 17:22:26,830 INFO ipython === bench console session ===
2025-09-19 17:22:26,830 INFO ipython === session end ===
2025-09-19 17:45:26,936 INFO ipython === bench console session ===
2025-09-19 17:45:26,937 INFO ipython import frappe
2025-09-19 17:45:26,938 INFO ipython # Test the Python-only implementation
2025-09-19 17:45:26,938 INFO ipython print("=== Testing Python-only Transit Document Status Update ===")
2025-09-19 17:45:26,938 INFO ipython # Get a test clearing file
2025-09-19 17:45:26,939 INFO ipython test_cf = frappe.get_doc("Clearing File", "CF-2025-0730")
2025-09-19 17:45:26,939 INFO ipython print(f"Original status: {test_cf.status}")
2025-09-19 17:45:26,940 INFO ipython # Test with Sea mode
2025-09-19 17:45:26,940 INFO ipython print("\n--- Testing Sea Mode ---")
2025-09-19 17:45:26,940 INFO ipython test_cf.mode_of_transport = "Sea"
2025-09-19 17:45:26,941 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,941 INFO ipython test_cf.document = []
2025-09-19 17:45:26,941 INFO ipython # Add required documents for Sea mode
2025-09-19 17:45:26,941 INFO ipython sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 17:45:26,942 INFO ipython for doc_name in sea_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,942 INFO ipython print(f"Mode: {test_cf.mode_of_transport}")
2025-09-19 17:45:26,942 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,943 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,943 INFO ipython # Test the function directly
2025-09-19 17:45:26,943 INFO ipython print(f"Status before check: {test_cf.status}")
2025-09-19 17:45:26,944 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:45:26,944 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:45:26,944 INFO ipython # Test with Air mode
2025-09-19 17:45:26,944 INFO ipython print("\n--- Testing Air Mode ---")
2025-09-19 17:45:26,945 INFO ipython test_cf.mode_of_transport = "Air"
2025-09-19 17:45:26,945 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,945 INFO ipython test_cf.document = []
2025-09-19 17:45:26,946 INFO ipython # Add required documents for Air mode
2025-09-19 17:45:26,946 INFO ipython air_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 17:45:26,946 INFO ipython for doc_name in air_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,946 INFO ipython print(f"Mode: {test_cf.mode_of_transport}")
2025-09-19 17:45:26,946 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,947 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,947 INFO ipython # Test the function directly
2025-09-19 17:45:26,947 INFO ipython print(f"Status before check: {test_cf.status}")
2025-09-19 17:45:26,947 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 17:45:26,948 INFO ipython print(f"Status after check: {test_cf.status}")
2025-09-19 17:45:26,948 INFO ipython print("\n=== Testing Complete ===")
2025-09-19 17:45:26,948 INFO ipython print("✅ Python-only implementation working correctly")
2025-09-19 17:45:26,948 INFO ipython # Test the before_save integration
2025-09-19 17:45:26,948 INFO ipython print("\n=== Testing before_save Integration ===")
2025-09-19 17:45:26,948 INFO ipython # Reset the test clearing file
2025-09-19 17:45:26,949 INFO ipython test_cf.mode_of_transport = "Sea"
2025-09-19 17:45:26,949 INFO ipython test_cf.status = "Draft"
2025-09-19 17:45:26,949 INFO ipython test_cf.document = []
2025-09-19 17:45:26,949 INFO ipython # Add required documents for Sea mode
2025-09-19 17:45:26,949 INFO ipython sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 17:45:26,949 INFO ipython for doc_name in sea_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 17:45:26,949 INFO ipython print(f"Status before save: {test_cf.status}")
2025-09-19 17:45:26,949 INFO ipython print("Documents attached:")
2025-09-19 17:45:26,950 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 17:45:26,950 INFO ipython # Test the before_save method which calls check_and_update_status
2025-09-19 17:45:26,950 INFO ipython test_cf.before_save()
2025-09-19 17:45:26,950 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 17:45:26,950 INFO ipython print("\n✅ before_save integration working correctly")
2025-09-19 17:45:26,950 INFO ipython print("The status will automatically update from Draft to Open when documents are attached and the form is saved.")
2025-09-19 17:45:26,950 INFO ipython === session end ===
2025-09-19 18:00:21,764 INFO ipython === bench console session ===
2025-09-19 18:00:21,765 INFO ipython import frappe
2025-09-19 18:00:21,765 INFO ipython # Let's debug step by step
2025-09-19 18:00:21,765 INFO ipython print("=== Debugging Transit Document Status Update ===")
2025-09-19 18:00:21,766 INFO ipython # Get a real clearing file
2025-09-19 18:00:21,766 INFO ipython clearing_files = frappe.get_all("Clearing File", fields=["name", "status", "mode_of_transport"], limit=3)
2025-09-19 18:00:21,766 INFO ipython print("Available clearing files:")
2025-09-19 18:00:21,767 INFO ipython for cf in clearing_files:
        print(f"  {cf.name}: Status={cf.status}, Mode={cf.mode_of_transport}")
        
2025-09-19 18:00:21,767 INFO ipython # Use the first one for testing
2025-09-19 18:00:21,768 INFO ipython if clearing_files:
        test_cf_name = clearing_files[0].name
            test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 18:00:21,769 INFO ipython     print(f"\nTesting with: {test_cf_name}")
2025-09-19 18:00:21,770 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,770 INFO ipython     print(f"Mode of transport: {test_cf.mode_of_transport}")
2025-09-19 18:00:21,770 INFO ipython     # Check current documents
2025-09-19 18:00:21,770 INFO ipython     print("Current documents:")
2025-09-19 18:00:21,771 INFO ipython     if test_cf.document:
            for doc in test_cf.document:
                        print(f"  - {doc.document_name}")
                        else:
                                print("  No documents attached")
2025-09-19 18:00:21,771 INFO ipython     # Let's test the function step by step
2025-09-19 18:00:21,771 INFO ipython     print("\n--- Testing check_transit_documents_and_update_status function ---")
2025-09-19 18:00:21,772 INFO ipython     # First, let's see what the function expects
2025-09-19 18:00:21,772 INFO ipython     base_required_docs = [
        "Authorisation Letter",
            "Commercial Invoice", 
                "Packing List"
                ]
2025-09-19 18:00:21,772 INFO ipython     if test_cf.mode_of_transport == "Air":
            required_docs = base_required_docs + ["Air Waybill (AWB)"]
            elif test_cf.mode_of_transport == "Sea":
                    required_docs = base_required_docs + ["Bill of Lading B/L"]
2025-09-19 18:00:21,773 INFO ipython     else:
            required_docs = base_required_docs + ["Air Waybill (AWB)"]
2025-09-19 18:00:21,773 INFO ipython     print(f"Required documents for {test_cf.mode_of_transport} mode:")
2025-09-19 18:00:21,773 INFO ipython     for doc in required_docs:
            print(f"  - {doc}")
                
2025-09-19 18:00:21,773 INFO ipython     # Check what's missing
2025-09-19 18:00:21,774 INFO ipython     attached_docs = []
2025-09-19 18:00:21,774 INFO ipython     if test_cf.document:
            attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
            
2025-09-19 18:00:21,774 INFO ipython     missing_docs = []
2025-09-19 18:00:21,775 INFO ipython     for required_doc in required_docs:
            if required_doc not in attached_docs:
                        missing_docs.append(required_doc)
                        
2025-09-19 18:00:21,775 INFO ipython     print(f"\nMissing documents: {missing_docs}")
2025-09-19 18:00:21,775 INFO ipython     print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,776 INFO ipython     print(f"Status check condition: {test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,776 INFO ipython else:
        print("No clearing files found")
2025-09-19 18:00:21,776 INFO ipython # Debug properly
2025-09-19 18:00:21,777 INFO ipython test_cf_name = clearing_files[0].name
2025-09-19 18:00:21,777 INFO ipython test_cf = frappe.get_doc("Clearing File", test_cf_name)
2025-09-19 18:00:21,777 INFO ipython print(f"Testing with: {test_cf_name}")
2025-09-19 18:00:21,777 INFO ipython print(f"Current status: {test_cf.status}")
2025-09-19 18:00:21,778 INFO ipython print(f"Mode of transport: {test_cf.mode_of_transport}")
2025-09-19 18:00:21,778 INFO ipython # Check current documents
2025-09-19 18:00:21,778 INFO ipython print("Current documents:")
2025-09-19 18:00:21,778 INFO ipython if test_cf.document:
        for doc in test_cf.document:
                    print(f"  - {doc.document_name}")
                    else:
                            print("  No documents attached")
2025-09-19 18:00:21,779 INFO ipython # Let's manually add the required documents and test
2025-09-19 18:00:21,779 INFO ipython print("\n--- Adding required documents for testing ---")
2025-09-19 18:00:21,779 INFO ipython test_cf.document = []  # Clear existing
2025-09-19 18:00:21,780 INFO ipython # Add required documents based on mode
2025-09-19 18:00:21,780 INFO ipython if test_cf.mode_of_transport == "Sea":
        required_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
        else:
                required_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 18:00:21,780 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                            
2025-09-19 18:00:21,780 INFO ipython print("Added documents:")
2025-09-19 18:00:21,781 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,781 INFO ipython # Test the function
2025-09-19 18:00:21,781 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,782 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,782 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,782 INFO ipython # Test the before_save method
2025-09-19 18:00:21,782 INFO ipython print(f"\nTesting before_save method...")
2025-09-19 18:00:21,783 INFO ipython test_cf.status = "Draft"  # Reset to Draft
2025-09-19 18:00:21,783 INFO ipython print(f"Status before before_save: {test_cf.status}")
2025-09-19 18:00:21,783 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,783 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,784 INFO ipython # Let's fix the syntax and add documents properly
2025-09-19 18:00:21,784 INFO ipython required_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 18:00:21,784 INFO ipython print("Adding documents for Sea mode:")
2025-09-19 18:00:21,784 INFO ipython for doc_name in required_docs:
        test_cf.append("document", {
                "document_name": doc_name,
                        "document_type": doc_name
                            })
                                print(f"  Added: {doc_name}")
2025-09-19 18:00:21,785 INFO ipython print("\nDocuments now attached:")
2025-09-19 18:00:21,785 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,785 INFO ipython # Now test the function
2025-09-19 18:00:21,785 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,786 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,786 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,786 INFO ipython # Let's also debug the function step by step
2025-09-19 18:00:21,786 INFO ipython print("\n--- Debugging the function logic ---")
2025-09-19 18:00:21,787 INFO ipython # Check what the function sees
2025-09-19 18:00:21,787 INFO ipython base_required_docs = [
    "Authorisation Letter",
        "Commercial Invoice",
            "Packing List"
            ]
2025-09-19 18:00:21,787 INFO ipython if test_cf.mode_of_transport == "Sea":
        required_transit_docs = base_required_docs + ["Bill of Lading B/L"]
        else:
                required_transit_docs = base_required_docs + ["Air Waybill (AWB)"]
2025-09-19 18:00:21,787 INFO ipython print(f"Required documents: {required_transit_docs}")
2025-09-19 18:00:21,787 INFO ipython # Get attached documents
2025-09-19 18:00:21,788 INFO ipython attached_docs = []
2025-09-19 18:00:21,788 INFO ipython if test_cf.document:
        attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
        
2025-09-19 18:00:21,788 INFO ipython print(f"Attached documents: {attached_docs}")
2025-09-19 18:00:21,788 INFO ipython # Check missing documents
2025-09-19 18:00:21,789 INFO ipython missing_transit_docs = []
2025-09-19 18:00:21,789 INFO ipython for required_doc in required_transit_docs:
        if required_doc not in attached_docs:
                    missing_transit_docs.append(required_doc)
                    
2025-09-19 18:00:21,789 INFO ipython print(f"Missing documents: {missing_transit_docs}")
2025-09-19 18:00:21,790 INFO ipython print(f"All documents present: {len(missing_transit_docs) == 0}")
2025-09-19 18:00:21,790 INFO ipython print(f"Status is Draft: {test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,790 INFO ipython print(f"Should update status: {not missing_transit_docs and test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,790 INFO ipython # Let's add documents properly one by one
2025-09-19 18:00:21,790 INFO ipython print("Current document count:", len(test_cf.document))
2025-09-19 18:00:21,791 INFO ipython # Add documents manually
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Bill of Lading B/L", "document_type": "Bill of Lading B/L"})
2025-09-19 18:00:21,791 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,791 INFO ipython print("Document count after adding:", len(test_cf.document))
2025-09-19 18:00:21,791 INFO ipython print("Documents attached:")
2025-09-19 18:00:21,792 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,792 INFO ipython # Now test the function again
2025-09-19 18:00:21,792 INFO ipython print(f"\nStatus before function call: {test_cf.status}")
2025-09-19 18:00:21,792 INFO ipython test_cf.check_transit_documents_and_update_status()
2025-09-19 18:00:21,792 INFO ipython print(f"Status after function call: {test_cf.status}")
2025-09-19 18:00:21,792 INFO ipython # Let's also check what the function sees now
2025-09-19 18:00:21,792 INFO ipython attached_docs = [doc.document_name for doc in test_cf.document if doc.document_name]
2025-09-19 18:00:21,792 INFO ipython print(f"Function sees attached docs: {attached_docs}")
2025-09-19 18:00:21,793 INFO ipython required_transit_docs = ["Authorisation Letter", "Commercial Invoice", "Packing List", "Bill of Lading B/L"]
2025-09-19 18:00:21,793 INFO ipython missing_docs = [doc for doc in required_transit_docs if doc not in attached_docs]
2025-09-19 18:00:21,793 INFO ipython print(f"Missing docs: {missing_docs}")
2025-09-19 18:00:21,793 INFO ipython print(f"Should update: {len(missing_docs) == 0 and test_cf.status in ['Draft', '']}")
2025-09-19 18:00:21,793 INFO ipython # Test the real-world scenario - save the document
2025-09-19 18:00:21,793 INFO ipython print("=== Testing Real-World Save Scenario ===")
2025-09-19 18:00:21,793 INFO ipython # Reset the clearing file
2025-09-19 18:00:21,794 INFO ipython test_cf.status = "Draft"
2025-09-19 18:00:21,794 INFO ipython test_cf.document = []
2025-09-19 18:00:21,794 INFO ipython print(f"Initial status: {test_cf.status}")
2025-09-19 18:00:21,794 INFO ipython print(f"Initial document count: {len(test_cf.document)}")
2025-09-19 18:00:21,794 INFO ipython # Add the required documents for Sea mode
2025-09-19 18:00:21,794 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Bill of Lading B/L", "document_type": "Bill of Lading B/L"})
2025-09-19 18:00:21,795 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,795 INFO ipython print(f"Documents added: {len(test_cf.document)}")
2025-09-19 18:00:21,795 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,796 INFO ipython # Test the before_save method (this is what happens when user saves)
2025-09-19 18:00:21,796 INFO ipython print(f"\nStatus before save: {test_cf.status}")
2025-09-19 18:00:21,796 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,796 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,797 INFO ipython # Now let's test with Air mode
2025-09-19 18:00:21,797 INFO ipython print("\n=== Testing Air Mode ===")
2025-09-19 18:00:21,797 INFO ipython test_cf.mode_of_transport = "Air"
2025-09-19 18:00:21,797 INFO ipython test_cf.status = "Draft"
2025-09-19 18:00:21,797 INFO ipython test_cf.document = []
2025-09-19 18:00:21,798 INFO ipython # Add required documents for Air mode
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Authorisation Letter", "document_type": "Authorisation Letter"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Commercial Invoice", "document_type": "Commercial Invoice"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Air Waybill (AWB)", "document_type": "Air Waybill (AWB)"})
2025-09-19 18:00:21,798 INFO ipython test_cf.append("document", {"document_name": "Packing List", "document_type": "Packing List"})
2025-09-19 18:00:21,799 INFO ipython print(f"Air mode documents added: {len(test_cf.document)}")
2025-09-19 18:00:21,799 INFO ipython for doc in test_cf.document:
        print(f"  - {doc.document_name}")
        
2025-09-19 18:00:21,799 INFO ipython print(f"Status before save: {test_cf.status}")
2025-09-19 18:00:21,799 INFO ipython test_cf.before_save()
2025-09-19 18:00:21,799 INFO ipython print(f"Status after before_save: {test_cf.status}")
2025-09-19 18:00:21,799 INFO ipython print("\n✅ Implementation is working correctly!")
2025-09-19 18:00:21,800 INFO ipython print("The status automatically changes from Draft to Open when all required documents are attached.")
2025-09-19 18:00:21,800 INFO ipython # Check what document types are available in the system
2025-09-19 18:00:21,800 INFO ipython print("=== Checking Available Document Types ===")
2025-09-19 18:00:21,800 INFO ipython # Get all clearing document types
2025-09-19 18:00:21,800 INFO ipython doc_types = frappe.get_all("Clearing Document Type", fields=["name", "linked_document"], order_by="name")
2025-09-19 18:00:21,800 INFO ipython print("Available Clearing Document Types:")
2025-09-19 18:00:21,800 INFO ipython for dt in doc_types:
        print(f"  - {dt.name} (linked to: {dt.linked_document})")
        
2025-09-19 18:00:21,801 INFO ipython # Check if our required documents exist
2025-09-19 18:00:21,801 INFO ipython required_sea_docs = ["Authorisation Letter", "Commercial Invoice", "Bill of Lading B/L", "Packing List"]
2025-09-19 18:00:21,801 INFO ipython required_air_docs = ["Authorisation Letter", "Commercial Invoice", "Air Waybill (AWB)", "Packing List"]
2025-09-19 18:00:21,801 INFO ipython print(f"\nRequired Sea documents:")
2025-09-19 18:00:21,801 INFO ipython for doc in required_sea_docs:
        exists = any(dt.name == doc for dt in doc_types)
            print(f"  - {doc}: {'✅ EXISTS' if exists else '❌ MISSING'}")
2025-09-19 18:00:21,801 INFO ipython print(f"\nRequired Air documents:")
2025-09-19 18:00:21,801 INFO ipython for doc in required_air_docs:
        exists = any(dt.name == doc for dt in doc_types)
            print(f"  - {doc}: {'✅ EXISTS' if exists else '❌ MISSING'}")
2025-09-19 18:00:21,801 INFO ipython # Also check if there are any similar document names
2025-09-19 18:00:21,801 INFO ipython print(f"\nDocument types containing 'Bill':")
2025-09-19 18:00:21,802 INFO ipython bill_docs = [dt.name for dt in doc_types if 'bill' in dt.name.lower() or 'lading' in dt.name.lower()]
2025-09-19 18:00:21,802 INFO ipython for doc in bill_docs:
        print(f"  - {doc}")
        
2025-09-19 18:00:21,802 INFO ipython print(f"\nDocument types containing 'Waybill' or 'AWB':")
2025-09-19 18:00:21,802 INFO ipython awb_docs = [dt.name for dt in doc_types if 'waybill' in dt.name.lower() or 'awb' in dt.name.lower()]
2025-09-19 18:00:21,802 INFO ipython for doc in awb_docs:
        print(f"  - {doc}")
        
2025-09-19 18:00:21,802 INFO ipython === session end ===
2025-10-01 12:30:37,878 INFO ipython === bench console session ===
2025-10-01 12:30:37,878 INFO ipython # Check current user roles
2025-10-01 12:30:37,878 INFO ipython frappe.get_roles()
2025-10-01 12:30:37,878 INFO ipython # Check if there are any workflow states for Clearing Charges
2025-10-01 12:30:37,878 INFO ipython frappe.get_all("Workflow State", filters={"parent": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,878 INFO ipython # Check if there's a workflow for Clearing Charges
2025-10-01 12:30:37,878 INFO ipython frappe.get_all("Workflow", filters={"document_type": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,879 INFO ipython # Check if Clearing Charges is submittable
2025-10-01 12:30:37,879 INFO ipython doc_meta = frappe.get_meta("Clearing Charges")
2025-10-01 12:30:37,879 INFO ipython print("Is submittable:", doc_meta.is_submittable)
2025-10-01 12:30:37,879 INFO ipython print("Has workflow:", doc_meta.has_workflow)
2025-10-01 12:30:37,879 INFO ipython # Check if there are any custom permissions
2025-10-01 12:30:37,879 INFO ipython custom_perms = frappe.get_all("Custom DocPerm", filters={"parent": "Clearing Charges"}, fields=["*"])
2025-10-01 12:30:37,879 INFO ipython print("Custom permissions:", custom_perms)
2025-10-01 12:30:37,879 INFO ipython # Let's check if there are any field-level restrictions
2025-10-01 12:30:37,879 INFO ipython # Get a sample Clearing Charges document to test
2025-10-01 12:30:37,879 INFO ipython sample_docs = frappe.get_all("Clearing Charges", limit=1)
2025-10-01 12:30:37,879 INFO ipython if sample_docs:
        doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
            print("Document status:", doc.status)
2025-10-01 12:30:37,880 INFO ipython     print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,880 INFO ipython     # Check if user has write permission
2025-10-01 12:30:37,880 INFO ipython     has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,880 INFO ipython     print("Has write permission:", has_write)
2025-10-01 12:30:37,880 INFO ipython else:
        print("No Clearing Charges documents found")
2025-10-01 12:30:37,880 INFO ipython sample_docs = frappe.get_all("Clearing Charges", limit=1)
2025-10-01 12:30:37,880 INFO ipython print("Found documents:", len(sample_docs))
2025-10-01 12:30:37,880 INFO ipython if sample_docs:
        doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
            print("Document status:", doc.status)
2025-10-01 12:30:37,880 INFO ipython     print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,880 INFO ipython     has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,880 INFO ipython     print("Has write permission:", has_write)
2025-10-01 12:30:37,880 INFO ipython doc = frappe.get_doc("Clearing Charges", sample_docs[0].name)
2025-10-01 12:30:37,880 INFO ipython print("Document status:", doc.status)
2025-10-01 12:30:37,881 INFO ipython print("Document docstatus:", doc.docstatus)
2025-10-01 12:30:37,881 INFO ipython has_write = frappe.has_permission("Clearing Charges", "write", doc=doc)
2025-10-01 12:30:37,881 INFO ipython print("Has write permission:", has_write)
2025-10-01 12:30:37,881 INFO ipython # Check if there are any field-level read_only settings
2025-10-01 12:30:37,881 INFO ipython meta = frappe.get_meta("Clearing Charges")
2025-10-01 12:30:37,881 INFO ipython for field in meta.fields:
        if field.read_only:
                    print(f"Field '{field.fieldname}' is read-only: {field.read_only}")
                    
2025-10-01 12:30:37,881 INFO ipython # Check if there are any depends_on conditions that might affect editability
2025-10-01 12:30:37,881 INFO ipython for field in meta.fields:
        if field.depends_on:
                    print(f"Field '{field.fieldname}' depends on: {field.depends_on}")
                            
2025-10-01 12:30:37,881 INFO ipython === session end ===
2025-10-01 12:32:14,055 INFO ipython === bench console session ===
2025-10-01 12:32:14,055 INFO ipython # Check what roles are needed for Clearing Charges
2025-10-01 12:32:14,056 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:32:14,056 INFO ipython print("DocPerm entries for Clearing Charges:")
2025-10-01 12:32:14,056 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:32:14,056 INFO ipython # Check current user
2025-10-01 12:32:14,056 INFO ipython print(f"\nCurrent user: {frappe.session.user}")
2025-10-01 12:32:14,056 INFO ipython # Check if current user has the required role
2025-10-01 12:32:14,056 INFO ipython user_roles = frappe.get_roles()
2025-10-01 12:32:14,056 INFO ipython print(f"User has System Manager role: {'System Manager' in user_roles}")
2025-10-01 12:32:14,056 INFO ipython # Check what clearing-related roles exist
2025-10-01 12:32:14,056 INFO ipython clearing_roles = frappe.get_all("Role", filters=[["name", "like", "%clear%"]], fields=["name"])
2025-10-01 12:32:14,057 INFO ipython print("Clearing-related roles:")
2025-10-01 12:32:14,057 INFO ipython for role in clearing_roles:
        print(f"- {role.name}")
        
2025-10-01 12:32:14,057 INFO ipython # Also check for TRA roles
2025-10-01 12:32:14,057 INFO ipython tra_roles = frappe.get_all("Role", filters=[["name", "like", "%TRA%"]], fields=["name"])
2025-10-01 12:32:14,057 INFO ipython print("\nTRA-related roles:")
2025-10-01 12:32:14,057 INFO ipython for role in tra_roles:
        print(f"- {role.name}")
        
2025-10-01 12:32:14,057 INFO ipython === session end ===
2025-10-01 12:33:15,836 INFO ipython === bench console session ===
2025-10-01 12:33:15,836 INFO ipython # Verify the new permissions have been applied
2025-10-01 12:33:15,836 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:33:15,837 INFO ipython print("Updated DocPerm entries for Clearing Charges:")
2025-10-01 12:33:15,837 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:33:15,837 INFO ipython === session end ===
2025-10-01 12:34:29,855 INFO ipython === bench console session ===
2025-10-01 12:34:29,856 INFO ipython doc_perms = frappe.get_all("DocPerm", filters={"parent": "Clearing Charges"}, fields=["role", "read", "write", "create", "delete"])
2025-10-01 12:34:29,856 INFO ipython print("Updated DocPerm entries for Clearing Charges:")
2025-10-01 12:34:29,856 INFO ipython for perm in doc_perms:
        print(f"Role: {perm.role}, Read: {perm.read}, Write: {perm.write}, Create: {perm.create}, Delete: {perm.delete}")
        
2025-10-01 12:34:29,856 INFO ipython # Test permissions for Clearing Agent role
2025-10-01 12:34:29,856 INFO ipython has_write_clearing_agent = frappe.has_permission("Clearing Charges", "write", user="Administrator")
2025-10-01 12:34:29,856 INFO ipython print(f"Administrator has write permission: {has_write_clearing_agent}")
2025-10-01 12:34:29,856 INFO ipython # Clear permissions cache to ensure fresh check
2025-10-01 12:34:29,856 INFO ipython frappe.clear_cache()
2025-10-01 12:34:29,856 INFO ipython === session end ===
